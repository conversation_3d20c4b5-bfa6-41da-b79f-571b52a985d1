#include <unordered_map>
#include <string>
#include <any>
#include <mutex>
#include <stdexcept>
#include <functional>
#include <type_traits>
#include <iostream>
#include <iomanip>
#include <sstream>
#include <vector>
#include <typeinfo>
#include <typeindex>
#include <memory>

class BaseDevice
{
protected:
    mutable std::mutex mutex_;
    std::unordered_map<std::string, std::any> value_map_;
    std::unordered_map<std::string, const std::type_info *> type_map_;

    // 类型转换工具
    template <typename To, typename From>
    static To convert_value(From value)
    {
        if constexpr (std::is_same_v<To, From>)
        {
            return value; // 相同类型直接返回
        }
        else if constexpr (std::is_convertible_v<From, To>)
        {
            return static_cast<To>(value); // 安全转换
        }
        else
        {
            // 无法转换的类型
            std::stringstream ss;
            ss << "Cannot convert from " << typeid(From).name()
               << " to " << typeid(To).name();
            throw std::runtime_error(ss.str());
        }
    }

    // 通用类型处理器
    template <typename T>
    T get_value_impl(const std::string &name) const
    {
        auto it = value_map_.find(name);
        if (it == value_map_.end())
        {
            // throw std::out_of_range("Value '" + name + "' not found");
            return T{};
        }

        try
        {
            // 尝试直接获取
            return std::any_cast<std::reference_wrapper<T>>(it->second).get();
        }
        catch (const std::bad_any_cast &)
        {
            // 尝试类型转换
            return convert_from_any<T>(it->second, name);
        }
    }

    template <typename T>
    void set_value_impl(const std::string &name, T value)
    {
        auto it = value_map_.find(name);
        if (it != value_map_.end())
        {
            // throw std::out_of_range("Value '" + name + "' not found");
            convert_to_any(it->second, value, name);
        }
    }

    // 从any转换到目标类型
    template <typename T>
    T convert_from_any(const std::any &any_value, const std::string &name) const
    {
        // 支持的类型列表
        if (any_value.type() == typeid(std::reference_wrapper<int>))
        {
            int val = std::any_cast<std::reference_wrapper<int>>(any_value).get();
            return convert_value<T>(val);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<double>))
        {
            double val = std::any_cast<std::reference_wrapper<double>>(any_value).get();
            return convert_value<T>(val);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<float>))
        {
            float val = std::any_cast<std::reference_wrapper<float>>(any_value).get();
            return convert_value<T>(val);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<bool>))
        {
            bool val = std::any_cast<std::reference_wrapper<bool>>(any_value).get();
            return convert_value<T>(val);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<long>))
        {
            long val = std::any_cast<std::reference_wrapper<long>>(any_value).get();
            return convert_value<T>(val);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<short>))
        {
            short val = std::any_cast<std::reference_wrapper<short>>(any_value).get();
            return convert_value<T>(val);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<char>))
        {
            char val = std::any_cast<std::reference_wrapper<char>>(any_value).get();
            return convert_value<T>(val);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<std::string>))
        {
            const std::string &val = std::any_cast<std::reference_wrapper<std::string>>(any_value).get();
            return convert_value<T>(val);
        }
        else
        {
            // std::stringstream ss;
            // ss << "Unsupported type conversion for value '" << name
            //    << "'. Stored type: " << any_value.type().name();
            // throw std::runtime_error(ss.str());
            int val = std::any_cast<std::reference_wrapper<int>>(any_value).get();
            return convert_value<T>(val);
        }
    }

    // 转换并设置到any
    template <typename T>
    void convert_to_any(std::any &any_value, T value, const std::string &name)
    {
        if (any_value.type() == typeid(std::reference_wrapper<int>))
        {
            auto ref = std::any_cast<std::reference_wrapper<int>>(any_value);
            ref.get() = convert_value<int>(value);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<double>))
        {
            auto ref = std::any_cast<std::reference_wrapper<double>>(any_value);
            ref.get() = convert_value<double>(value);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<float>))
        {
            auto ref = std::any_cast<std::reference_wrapper<float>>(any_value);
            ref.get() = convert_value<float>(value);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<bool>))
        {
            auto ref = std::any_cast<std::reference_wrapper<bool>>(any_value);
            ref.get() = convert_value<bool>(value);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<long>))
        {
            auto ref = std::any_cast<std::reference_wrapper<long>>(any_value);
            ref.get() = convert_value<long>(value);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<short>))
        {
            auto ref = std::any_cast<std::reference_wrapper<short>>(any_value);
            ref.get() = convert_value<short>(value);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<char>))
        {
            auto ref = std::any_cast<std::reference_wrapper<char>>(any_value);
            ref.get() = convert_value<char>(value);
        }
        else if (any_value.type() == typeid(std::reference_wrapper<std::string>))
        {
            auto ref = std::any_cast<std::reference_wrapper<std::string>>(any_value);
            ref.get() = convert_value<std::string>(value);
        }
        else
        {
            // std::stringstream ss;
            // ss << "Unsupported type conversion for value '" << name
            //    << "'. Stored type: " << any_value.type().name();
            // throw std::runtime_error(ss.str());
            // 未知的按整数处理。
            auto ref = std::any_cast<std::reference_wrapper<int>>(any_value);
            ref.get() = convert_value<int>(value);
        }
    }

public:
    // 注册成员变量
    template <typename T>
    void register_value(const std::string &label, T &value)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        if (value_map_.find(label) != value_map_.end())
        {
            throw std::invalid_argument("Value '" + label + "' already registered");
        }

        value_map_[label] = std::ref(value);
        type_map_[label] = &typeid(T);
    }

    // 获取值（带自动类型转换）
    template <typename T>
    T get_value(const std::string &name) const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        return get_value_impl<T>(name);
    }

    // 设置值（带自动类型转换）
    template <typename T>
    void set_value(const std::string &name, T value)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        set_value_impl(name, value);
    }

    // 获取实际存储类型
    const std::type_info &get_stored_type(const std::string &name) const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto it = type_map_.find(name);
        if (it == type_map_.end())
        {
            throw std::out_of_range("Value '" + name + "' not found");
        }

        return *(it->second);
    }

    // 获取类型名称
    std::string get_type_name(const std::string &name) const
    {
        return get_stored_type(name).name();
    }

    // 检查值是否存在
    bool has_value(const std::string &name) const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        return value_map_.find(name) != value_map_.end();
    }

    // 获取所有注册的值名称
    std::vector<std::string> get_value_names() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        std::vector<std::string> names;
        names.reserve(value_map_.size());

        for (const auto &pair : value_map_)
        {
            names.push_back(pair.first);
        }

        return names;
    }

    // 移除注册的值
    void unregister_value(const std::string &name)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        auto it = value_map_.find(name);
        if (it == value_map_.end())
        {
            throw std::out_of_range("Value '" + name + "' not found");
        }

        value_map_.erase(it);
        type_map_.erase(name);
    }

    // 打印所有注册的值及其类型
    void print_registered_values() const
    {
        std::lock_guard<std::mutex> lock(mutex_);

        std::cout << "Registered values:\n";
        for (const auto &pair : type_map_)
        {
            std::cout << "  " << pair.first << " (" << pair.second->name() << ")\n";
        }
    }
};

enum class MyTYpe
{
    A = 1,
    B = 2,
    C = 3,
    D = 4
};

// 测试类
class Motor : public BaseDevice
{
private:
    double speed = 0.0;
    bool is_running = false;
    int error_code = 0;
    float temperature = 25.5f;
    long total_runtime = 0L;
    MyTYpe my_type = MyTYpe::A;
    std::string status_message = "OK";

public:
    Motor()
    {
        register_value("speed", speed);
        register_value("running", is_running);
        register_value("error", error_code);
        register_value("temperature", temperature);
        register_value("runtime", total_runtime);
        register_value("status", status_message);
        // register_value("my_type", my_type);
    }

    void print_status() const
    {
        std::cout << "Motor Status:\n"
                  << "  Running: " << std::boolalpha << is_running << "\n"
                  << "  Speed: " << speed << "\n"
                  << "  Error: " << error_code << "\n"
                  << "  Temperature: " << temperature << "°C\n"
                  << "  Runtime: " << total_runtime << " hours\n"
                  << "  Status: " << status_message << "\n";
    }

    // 模拟运行
    void start()
    {
        is_running = true;
        speed = 1000.0;
        status_message = "Running";
    }

    void stop()
    {
        is_running = false;
        speed = 0.0;
        status_message = "Stopped";
    }
};

int main()
{
    // try
    // {
    Motor motor;

    motor.set_value("speed", 500.0);
    motor.set_value("running", false);
    motor.set_value("error", 1234.000);
     motor.set_value("my_type", 2.0);

    double spd = motor.get_value<double>("speed");
    double run = motor.get_value<double>("running");
    double err = motor.get_value<double>("error");
    // double mt = motor.get_value<double>("my_type");

    std::cout << "spd=" << spd << std::endl;
    std::cout << "run=" << run << std::endl;
    std::cout << "err=" << err << std::endl;
    // std::cout << "my_type=" << mt << std::endl;

    motor.print_status();

    //     std::cout << "=== 初始状态 ===" << std::endl;
    //     motor.print_status();

    //     std::cout << "\n=== 注册的值列表 ===" << std::endl;
    //     motor.print_registered_values();

    //     std::cout << "\n=== 测试自动类型转换 - 设置 ===" << std::endl;
    //     motor.set_value("running", 1);                    // int -> bool (true)
    //     motor.set_value("speed", 1500);                   // int -> double
    //     motor.set_value("error", 3.7);                    // double -> int (truncated to 3)
    //     motor.set_value("temperature", 85);               // int -> float
    //     motor.set_value("runtime", 1000.5);               // double -> long
    //     motor.set_value("status", std::string("Active")); // string -> string

    //     std::cout << "设置后的状态:" << std::endl;
    //     motor.print_status();

    //     std::cout << "\n=== 测试自动类型转换 - 获取 ===" << std::endl;
    //     bool running_bool = motor.get_value<bool>("running");
    //     double running_double = motor.get_value<double>("running"); // true -> 1.0
    //     int speed_int = motor.get_value<int>("speed");              // 1500.0 -> 1500
    //     double error_double = motor.get_value<double>("error");     // 3 -> 3.0
    //     int temp_int = motor.get_value<int>("temperature");         // 85.0f -> 85
    //     double runtime_double = motor.get_value<double>("runtime"); // 1000L -> 1000.0

    //     std::cout << "获取的转换值:"
    //               << "\n  running as bool: " << std::boolalpha << running_bool
    //               << "\n  running as double: " << running_double
    //               << "\n  speed as int: " << speed_int
    //               << "\n  error as double: " << error_double
    //               << "\n  temperature as int: " << temp_int
    //               << "\n  runtime as double: " << runtime_double
    //               << std::endl;

    //     std::cout << "\n=== 测试类型信息 ===" << std::endl;
    //     auto names = motor.get_value_names();
    //     for (const auto &name : names)
    //     {
    //         std::cout << "  " << name << ": " << motor.get_type_name(name) << std::endl;
    //     }

    //     std::cout << "\n=== 测试模拟操作 ===" << std::endl;
    //     motor.start();
    //     std::cout << "启动后:" << std::endl;
    //     motor.print_status();

    //     // 通过监控接口修改值
    //     motor.set_value("temperature", 95.5);
    //     motor.set_value("runtime", motor.get_value<long>("runtime") + 10);

    //     std::cout << "\n运行一段时间后:" << std::endl;
    //     motor.print_status();

    //     motor.stop();
    //     std::cout << "\n停止后:" << std::endl;
    //     motor.print_status();

    //     std::cout << "\n=== 测试错误处理 ===" << std::endl;

    //     // 测试不存在的值
    //     try
    //     {
    //         motor.get_value<int>("nonexistent");
    //     }
    //     catch (const std::exception &e)
    //     {
    //         std::cout << "预期错误 (不存在的值): " << e.what() << std::endl;
    //     }

    //     // 测试重复注册
    //     try
    //     {
    //         double dummy = 0.0;
    //         motor.register_value("speed", dummy);
    //     }
    //     catch (const std::exception &e)
    //     {
    //         std::cout << "预期错误 (重复注册): " << e.what() << std::endl;
    //     }

    //     // 测试不支持的类型转换
    //     try
    //     {
    //         motor.set_value("speed", std::string("invalid"));
    //     }
    //     catch (const std::exception &e)
    //     {
    //         std::cout << "预期错误 (不支持的转换): " << e.what() << std::endl;
    //     }

    //     std::cout << "\n=== 测试值的移除 ===" << std::endl;
    //     std::cout << "移除前的值数量: " << motor.get_value_names().size() << std::endl;

    //     // 注意：在实际应用中，移除仍在使用的成员变量引用是危险的
    //     // 这里只是演示功能
    //     motor.unregister_value("error");
    //     std::cout << "移除 'error' 后的值数量: " << motor.get_value_names().size() << std::endl;

    //     try
    //     {
    //         motor.get_value<int>("error");
    //     }
    //     catch (const std::exception &e)
    //     {
    //         std::cout << "预期错误 (已移除的值): " << e.what() << std::endl;
    //     }
    // }
    // catch (const std::exception &e)
    // {
    //     std::cerr << "未处理的错误: " << e.what() << std::endl;
    //     return 1;
    // }

    std::cout << "\n=== 测试完成 ===" << std::endl;
    return 0;
}
