#include <fstream>
#include <sstream>
#include <string>
#include <unordered_map>
#include <vector>
#include <cstdint>
#include <algorithm>
#include <iostream>

struct ExchangeParamInfo
{
    uint16_t ID;
    std::string device_name;
    std::string paremeter_name;
    std::string description;
};

std::unordered_map<uint8_t, ExchangeParamInfo> load_from_hash_file(const std::string &filename)
{
    std::unordered_map<uint8_t, ExchangeParamInfo> paramMap;
    std::ifstream file(filename);

    if (!file.is_open())
    {
        return paramMap; // 返回空map
    }

    // 1. 处理版本行
    std::string line;
    if (!std::getline(file, line) || line.find("version:") != 0)
    {
        return paramMap;
    }

    // 提取版本号并组合成32位数
    std::string verStr = line.substr(8); // 跳过"version:"
    std::replace(verStr.begin(), verStr.end(), '.', ' ');
    std::istringstream verStream(verStr);

    uint32_t version = 0;
    uint16_t part1, part2;
    uint32_t part3;

    if (verStream >> part1 >> part2 >> part3)
    {
        version = (static_cast<uint32_t>(part1) << 24) |
                  (static_cast<uint32_t>(part2) << 16) |
                  (part3 & 0xFFFF);
    }
    else
    {
        return paramMap; // 版本格式错误
    }
    std::cout << "version=" << version << std::endl;
    // 2. 检查魔法字符串
    if (!std::getline(file, line) || line != "arlon@wangxiaolong@2025.8")
    {
        return paramMap;
    }

    // 版本号转换为3个字节
    // 将32位版本号拆解回三个部分
    uint8_t major = (version >> 24) & 0xFF;
    uint8_t minor = (version >> 16) & 0xFF;
    uint16_t patch = version & 0xFFFF;

    std::cout << "version=" << version
              << " (major=" << static_cast<int>(major)
              << ", minor=" << static_cast<int>(minor)
              << ", patch=" << patch << ")"
              << std::endl;

    // 3. 处理数据行
    while (std::getline(file, line))
    {
        // 移除行尾的'\r'（处理Windows换行符）
        if (!line.empty() && line.back() == '\r')
        {
            line.pop_back();
        }

        // 跳过空行和注释行（以'#'开头）
        if (line.empty() || line[0] == '#')
        {
            continue;
        }

        // 统计逗号数量
        size_t commaCount = std::count(line.begin(), line.end(), ',');
        if (commaCount < 3)
        {
            continue; // 至少需要3个逗号
        }

        // 找到前三个逗号的位置
        size_t pos1 = line.find(',');
        size_t pos2 = line.find(',', pos1 + 1);
        size_t pos3 = line.find(',', pos2 + 1);

        if (pos1 == std::string::npos ||
            pos2 == std::string::npos ||
            pos3 == std::string::npos)
        {
            continue; // 无效的分隔符位置
        }

        try
        {
            // 提取ID字段并转换
            std::string idStr = line.substr(0, pos1);
            uint16_t id = static_cast<uint16_t>(std::stoi(idStr));

            // 检查ID范围
            if (id > 32767)
            {
                continue; // ID超出范围
            }

            // 检查ID是否重复
            uint8_t key = static_cast<uint8_t>(id);
            if (paramMap.find(key) != paramMap.end())
            {
                continue; // 跳过重复ID
            }

            // 提取各字段
            std::string deviceName = line.substr(pos1 + 1, pos2 - pos1 - 1);
            std::string paramName = line.substr(pos2 + 1, pos3 - pos2 - 1);
            std::string description = line.substr(pos3 + 1);

            // 构建结构体并插入map
            paramMap[key] = ExchangeParamInfo{
                id,
                deviceName,
                paramName,
                description};
        }
        catch (...)
        {
            // 忽略转换失败的行
            continue;
        }
    }

    return paramMap;
};

int main()
{
    auto params = load_from_hash_file("device_params.hash");
    for (const auto &[id, info] : params)
    {
        std::cout << "Key: " << static_cast<int>(id)
                  << " | Device: " << info.device_name
                  << " | Param: " << info.paremeter_name
                  << " | Desc: " << info.description << "\n";
    }
    system("pause");
    return 0;
}