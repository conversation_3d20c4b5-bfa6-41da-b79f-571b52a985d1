#include "json_parser.h"
#include <iostream>

int main() {
    try {
        // 测试基本数据类型解析
        std::cout << "=== Testing Basic Parsing ===" << std::endl;
        JsonParser::JsonValue numberValue = JsonParser::parse("123.45");
        std::cout << "Parsed number: " << JsonParser::stringify(numberValue) << std::endl;

        JsonParser::JsonValue stringValue = JsonParser::parse("\"Hello, World!\"");
        std::cout << "Parsed string: " << JsonParser::stringify(stringValue) << std::endl;

        JsonParser::JsonValue boolValue = JsonParser::parse("true");
        std::cout << "Parsed boolean: " << JsonParser::stringify(boolValue) << std::endl;

        // 测试编码
        std::cout << "\n=== Testing Stringify ===" << std::endl;
        JsonParser::JsonValue testValue;
        testValue.type = JsonParser::JsonValueType::Number;
        testValue.number = 42;
        std::cout << "Stringified number: " << JsonParser::stringify(testValue) << std::endl;

        // 测试文件加载和保存
        std::cout << "\n=== Testing File Operations ===" << std::endl;
        JsonParser::JsonValue fileValue = JsonParser::loadFromFile("test.json");
        std::cout << "Loaded from file: " << JsonParser::stringify(fileValue) << std::endl;

        JsonParser::saveToFile("output.json", fileValue);
        std::cout << "Saved to file: output.json" << std::endl;

        // 测试无效 JSON 数据
        std::cout << "\n=== Testing Error Handling ===" << std::endl;
        try {
            JsonParser::JsonValue invalidValue = JsonParser::parse("invalid");
        } catch (const std::exception& e) {
            std::cerr << "Expected error: " << e.what() << std::endl;
        }

        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Unexpected error: " << e.what() << std::endl;
        return 1;
    }
}