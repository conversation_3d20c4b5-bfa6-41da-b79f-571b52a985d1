#ifndef JSON_PARSER_H
#define JSON_PARSER_H

#include <string>
#include <map>
#include <vector>
#include <fstream>

namespace JsonParser {

    // JSON值的类型
    enum class JsonValueType {
        Null,
        Boolean,
        Number,
        String,
        Array,
        Object
    };

    // JSON值
    struct JsonValue {
        JsonValueType type;
        union {
            bool boolean;
            double number;
            std::string* string;
            std::vector<JsonValue>* array;
            std::map<std::string, JsonValue>* object;
        };

        JsonValue();
        ~JsonValue();
    };

    // 解析JSON字符串
    JsonValue parse(const std::string& json);

    // 将JsonValue编码为JSON字符串
    std::string stringify(const JsonValue& value);

    // 从JSON文件加载
    JsonValue loadFromFile(const std::string& filePath);

    // 将JsonValue保存到JSON文件
    void saveToFile(const std::string& filePath, const JsonValue& value);

} // namespace JsonParser

#endif // JSON_PARSER_H