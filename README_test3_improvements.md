# BaseDevice 成员变量监控系统 - 改进版本

## 概述

这是一个基于 C++17 的成员变量监控系统，允许从外部监控和修改类内部的成员变量，并支持自动类型转换。

## 主要功能

### 1. 成员变量注册
- 使用 `register_value(label, variable)` 注册成员变量
- 支持多种基本类型：int, double, float, bool, long, short, char, std::string
- 使用 `std::reference_wrapper` 存储变量引用，避免拷贝

### 2. 自动类型转换
- **获取值**: `get_value<T>(name)` - 自动将存储类型转换为请求类型
- **设置值**: `set_value(name, value)` - 自动将输入值转换为存储类型
- 支持安全的类型转换（如 int ↔ double, bool ↔ numeric）

### 3. 类型信息查询
- `get_stored_type(name)` - 获取变量的实际存储类型
- `get_type_name(name)` - 获取类型名称字符串
- `print_registered_values()` - 打印所有注册变量及其类型

### 4. 实用功能
- `has_value(name)` - 检查变量是否已注册
- `get_value_names()` - 获取所有注册变量名称列表
- `unregister_value(name)` - 移除已注册的变量

### 5. 线程安全
- 使用 `std::mutex` 保护所有操作
- 支持多线程并发访问

## 改进点

### 相比原版本的改进：

1. **代码重构**：
   - 消除了大量重复代码
   - 使用模板函数 `get_value_impl` 和 `set_value_impl` 统一处理
   - 分离类型转换逻辑到 `convert_from_any` 和 `convert_to_any`

2. **扩展类型支持**：
   - 新增支持：long, short, char, std::string
   - 更容易添加新类型支持

3. **更好的类型管理**：
   - 使用 `std::type_info*` 存储类型信息
   - 提供更清晰的类型查询接口

4. **增强的实用功能**：
   - 变量存在性检查
   - 变量列表获取
   - 变量注销功能
   - 调试信息打印

5. **更好的错误处理**：
   - 详细的错误信息
   - 类型转换失败的明确提示

## 使用示例

```cpp
class Motor : public BaseDevice {
private:
    double speed = 0.0;
    bool is_running = false;
    int error_code = 0;

public:
    Motor() {
        register_value("speed", speed);
        register_value("running", is_running);
        register_value("error", error_code);
    }
};

// 使用
Motor motor;

// 设置值（自动类型转换）
motor.set_value("running", 1);        // int -> bool
motor.set_value("speed", 1500);       // int -> double
motor.set_value("error", 3.7);        // double -> int (截断)

// 获取值（自动类型转换）
bool running = motor.get_value<bool>("running");
int speed_int = motor.get_value<int>("speed");
double error_double = motor.get_value<double>("error");

// 查询信息
auto names = motor.get_value_names();
std::string type_name = motor.get_type_name("speed");
```

## 技术特点

1. **类型安全**：使用 `std::any` 和 `std::reference_wrapper` 确保类型安全
2. **性能优化**：避免不必要的拷贝，直接操作原始变量
3. **扩展性**：易于添加新的类型支持
4. **异常安全**：完整的错误处理和异常传播

## 注意事项

1. **生命周期管理**：确保被监控的变量在 BaseDevice 对象生命周期内有效
2. **类型转换**：某些类型转换可能导致精度损失（如 double -> int）
3. **线程安全**：虽然内部操作是线程安全的，但外部对原始变量的直接访问不受保护

## 编译要求

- C++17 或更高版本
- 支持 `std::any` 的编译器（GCC 7+, Clang 4+, MSVC 2017+）

## 测试结果

程序成功通过了以下测试：
- ✅ 基本类型转换（int, double, float, bool, long, string）
- ✅ 错误处理（不存在的变量、重复注册、不支持的转换）
- ✅ 变量管理（注册、注销、查询）
- ✅ 线程安全操作
- ✅ 类型信息查询
