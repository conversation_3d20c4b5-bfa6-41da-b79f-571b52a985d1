#include "json_parser.h"
#include <iostream>
#include <sstream>
#include <stdexcept>
#include <fstream>

namespace JsonParser {

    JsonValue::JsonValue() : type(JsonValueType::Null) {}

    JsonValue::~JsonValue() {
        switch (type) {
            case JsonValueType::String:
                delete string;
                break;
            case JsonValueType::Array:
                delete array;
                break;
            case JsonValueType::Object:
                delete object;
                break;
            default:
                break;
        }
    }

    JsonValue parse(const std::string& json) {
        JsonValue value;
        size_t pos = 0;

        // 跳过空白字符
        auto skipWhitespace = [&]() {
            while (pos < json.size() && isspace(json[pos])) pos++;
        };

        // 初始检查
        skipWhitespace();
        if (pos >= json.size()) {
            std::cerr << "Empty JSON content" << std::endl;
            throw std::runtime_error("Empty JSON");
        }

        // 解析字符串
        auto parseString = [&]() {
            if (pos >= json.size() || json[pos] != '"') {
                std::cerr << "Error at position " << pos << ": Expected \"' at start of string" << std::endl;
                std::cerr << "Context: '" << json.substr(pos > 10 ? pos-10 : 0, 20) << "'" << std::endl;
                throw std::runtime_error("Expected \"'");
            }
            
            std::string str;
            pos++; // 跳过开头引号
            while (pos < json.size()) {
                if (json[pos] == '"') {
                    pos++; // 跳过结尾引号
                    return str;
                }
                
                if (json[pos] == '\\') {
                    pos++; // 处理转义字符
                    if (pos >= json.size()) {
                        std::cerr << "Error at position " << pos << ": Incomplete escape sequence" << std::endl;
                        throw std::runtime_error("Incomplete escape sequence");
                    }
                    switch (json[pos]) {
                        case '"': str += '"'; break;
                        case '\\': str += '\\'; break;
                        case '/': str += '/'; break;
                        case 'b': str += '\b'; break;
                        case 'f': str += '\f'; break;
                        case 'n': str += '\n'; break;
                        case 'r': str += '\r'; break;
                        case 't': str += '\t'; break;
                        default: throw std::runtime_error("Invalid escape sequence");
                    }
                } else {
                    str += json[pos];
                }
                pos++;
            }
            pos++; // 跳过结尾引号
            return str;
        };

        // 解析数组
        auto parseArray = [&]() {
            std::vector<JsonValue> arr;
            pos++; // 跳过 '['
            skipWhitespace();
            while (pos < json.size() && json[pos] != ']') {
                arr.push_back(parse(json.substr(pos)));
                skipWhitespace();
                if (json[pos] == ',') {
                    pos++;
                    skipWhitespace();
                }
            }
            if (pos >= json.size()) throw std::runtime_error("Unterminated array");
            pos++; // 跳过 ']'
            return arr;
        };

        // 解析对象
        auto parseObject = [&]() {
            std::map<std::string, JsonValue> obj;
            pos++; // 跳过 '{'
            skipWhitespace();
            while (pos < json.size() && json[pos] != '}') {
                if (json[pos] != '"') throw std::runtime_error("Expected key string");
                std::string key = parseString();
                skipWhitespace();
                skipWhitespace();
                if (json[pos] != ':') {
                    std::cerr << "Error at position " << pos << ": Expected ':' after key '" << key << "'" << std::endl;
                    std::cerr << "Context: '" << json.substr(pos > 10 ? pos-10 : 0, 20) << "'" << std::endl;
                    throw std::runtime_error("Expected ':'");
                }
                pos++; // 跳过 ':'
                skipWhitespace();
                if (pos >= json.size()) {
                    std::cerr << "Unexpected end of JSON after key '" << key << "'" << std::endl;
                    throw std::runtime_error("Unexpected end of JSON");
                }
                pos++;
                skipWhitespace();
                pos++; // 跳过 ':'
                skipWhitespace();
                JsonValue val = parse(json.substr(pos));
                obj[key] = val;
                skipWhitespace();
                if (json[pos] == ',') {
                    pos++;
                    skipWhitespace();
                }
            }
            if (pos >= json.size()) throw std::runtime_error("Unterminated object");
            pos++; // 跳过 '}'
            return obj;
        };

        skipWhitespace();
        if (pos >= json.size()) throw std::runtime_error("Empty JSON");

        switch (json[pos]) {
            case 'n': // null
                if (json.substr(pos, 4) == "null") {
                    value.type = JsonValueType::Null;
                    pos += 4;
                } else {
                    throw std::runtime_error("Invalid JSON value");
                }
                break;
            case 't': // true
                if (json.substr(pos, 4) == "true") {
                    value.type = JsonValueType::Boolean;
                    value.boolean = true;
                    pos += 4;
                } else {
                    throw std::runtime_error("Invalid JSON value");
                }
                break;
            case 'f': // false
                if (json.substr(pos, 5) == "false") {
                    value.type = JsonValueType::Boolean;
                    value.boolean = false;
                    pos += 5;
                } else {
                    throw std::runtime_error("Invalid JSON value");
                }
                break;
            case '"': // string
                value.type = JsonValueType::String;
                value.string = new std::string(parseString());
                break;
            case '[': // array
                value.type = JsonValueType::Array;
                value.array = new std::vector<JsonValue>(parseArray());
                break;
            case '{': // object
                value.type = JsonValueType::Object;
                value.object = new std::map<std::string, JsonValue>(parseObject());
                break;
            default: // number
                try {
                    size_t end;
                    double num = std::stod(json.substr(pos), &end);
                    value.type = JsonValueType::Number;
                    value.number = num;
                    pos += end;
                } catch (...) {
                    std::cerr << "Invalid JSON value at position " << pos << ": '" << json.substr(pos > 10 ? pos-10 : 0, 20) << "'" << std::endl;
                    throw std::runtime_error("Invalid JSON value");
                }
        }

        return value;
    }

    std::string stringify(const JsonValue& value) {
        std::ostringstream oss;
        switch (value.type) {
            case JsonValueType::Null:
                oss << "null";
                break;
            case JsonValueType::Boolean:
                oss << (value.boolean ? "true" : "false");
                break;
            case JsonValueType::Number:
                oss << value.number;
                break;
            case JsonValueType::String:
                oss << '"' << *value.string << '"';
                break;
            default:
                throw std::runtime_error("Unsupported JSON type for stringify");
        }
        return oss.str();
    }

    JsonValue loadFromFile(const std::string& filePath) {
        std::ifstream file(filePath);
        if (!file.is_open()) {
            throw std::runtime_error("Failed to open file: " + filePath);
        }
        std::string json((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
        std::cout << "Loaded JSON content: " << json << std::endl; // 调试输出
        JsonValue result = parse(json);
        std::cout << "Parsed successfully!" << std::endl; // 调试输出
        return result;
    }

    void saveToFile(const std::string& filePath, const JsonValue& value) {
        std::ofstream file(filePath);
        if (!file.is_open()) {
            throw std::runtime_error("Failed to open file: " + filePath);
        }
        file << stringify(value);
    }

} // namespace JsonParser