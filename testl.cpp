
#include <iostream>
#include <cstdint>
#include <string_view>
#include <string>
using namespace std;
bool isPrefix(std::string_view str, std::string_view prefix)
{
    if (str.length() < prefix.length())
        return false;
    return str.substr(0, prefix.length()) == prefix;
}

int main()
{
    bool a = false;

    int b = static_cast<int>(a);
     string test="abcdefghijklmnopqrstuvwxyz";

    if (isPrefix(test, "abc"))
    {
        cout<<"prefix abc found"<<endl;
    }
    else
    {
        cout<<"not found"<<endl;
    }

    cout << "a=" << a << ",b=" << b << endl;
    return 0;
}